# Referral System Testing Guide

This document provides comprehensive testing procedures for the newly implemented referral system with perks.

## Pre-Testing Setup

### 1. Run Migration Scripts

Before testing, ensure the system is properly set up:

```bash
# Run the complete setup
npm run setup:referrals

# Or run individual scripts if needed
npm run init:global-perks
npm run migrate:referral-codes
```

### 2. Verify Database Setup

Check Firebase Console to ensure:

- `referral/global/perks` collection contains 2 global perk definitions
- `referral` collection contains referral codes for existing users
- All existing users have referral codes

## Testing Checklist

### ✅ Referral Code Generation

#### Test 1: New User Signup with Referral Code Generation

- [ ] Create a new user account
- [ ] Verify referral code is automatically generated (8 alphanumeric characters)
- [ ] Check that `referral/{code}` document is created with correct structure
- [ ] Verify user can see their referral code in Settings > Referrals

#### Test 2: Existing User Referral Code Access

- [ ] Login as an existing user (migrated)
- [ ] Navigate to Settings > Referrals
- [ ] Verify referral code is displayed
- [ ] Verify referral link is generated correctly

#### Test 3: Referral Code Uniqueness

- [ ] Create multiple new users
- [ ] Verify all generated codes are unique
- [ ] Check no collisions in `referral` collection

### ✅ Referral Code Sharing

#### Test 4: Referral Link Copying

- [ ] Go to Settings > Referrals
- [ ] Click copy button for referral code
- [ ] Verify code is copied to clipboard
- [ ] Click copy button for referral link
- [ ] Verify full link is copied (togeda.ai/r/{code})

#### Test 5: Short Link Redirect

- [ ] Visit `/r/{valid-code}` directly
- [ ] Verify redirect to `/signup?referral_code={code}`
- [ ] Visit `/r/INVALID123`
- [ ] Verify redirect to `/signup` (without referral code)

#### Test 6: Social Sharing

- [ ] Test native share functionality (mobile)
- [ ] Verify fallback to copy on unsupported browsers

### ✅ Signup Flow with Referral Codes

#### Test 7: Referral Code Input Field

- [ ] Visit `/signup`
- [ ] Verify "Referral Code (Optional)" field is present
- [ ] Test input validation (8 characters, alphanumeric)
- [ ] Verify field converts to uppercase

#### Test 8: URL Parameter Pre-population

- [ ] Visit `/signup?referral_code=ABC12345`
- [ ] Verify referral code field is pre-populated
- [ ] Verify field remains editable
- [ ] Complete signup and verify referral is processed

#### Test 9: Referral Processing on Signup

- [ ] Get a valid referral code from existing user
- [ ] Create new account using that code
- [ ] Verify referral record is created in referrer's collection
- [ ] Verify referrer's total referral count is incremented
- [ ] Check for any newly unlocked perks

### ✅ Perk System

#### Test 10: Perk Unlocking Thresholds

- [ ] Create user with 0 referrals - verify no perks unlocked
- [ ] Process 5 referrals - verify squad perk is unlocked
- [ ] Process 10 referrals - verify subscription perk is unlocked
- [ ] Check perk status in user's perks collection

#### Test 11: Perk Display in Settings

- [ ] Navigate to Settings > Referrals
- [ ] Verify referral goals are displayed correctly
- [ ] Check progress bars show correct percentages
- [ ] Verify completed goals show "Unlocked" badge

#### Test 12: Achievement Popups

- [ ] Unlock a new perk (reach 5 or 10 referrals)
- [ ] Navigate to Settings > Referrals
- [ ] Verify achievement popup appears
- [ ] Verify popup auto-closes after 5 seconds
- [ ] Verify popup only shows once per achievement

### ✅ Perk-Aware Limits

#### Test 13: Squad Limit Enhancement

- [ ] User with squad perk should have +1 squad limit
- [ ] Test squad creation with enhanced limits
- [ ] Verify perk enhancements are displayed in UI

#### Test 14: Subscription Perk Application

- [ ] User with subscription perk should have it applied on login
- [ ] Check perk status changes from "unlocked" to "applied"
- [ ] Verify subscription duration is extended (if implemented)

#### Test 15: Perk-Aware Service Integration

- [ ] Test `PerkAwareSubscriptionService.getEnhancedUserLimits()`
- [ ] Verify enhanced limits include perk bonuses
- [ ] Test squad and trip creation with enhanced limits

### ✅ Real-time Updates

#### Test 16: Real-time Referral Updates

- [ ] Open Settings > Referrals in one browser
- [ ] Process a referral in another browser/device
- [ ] Verify referral count updates in real-time
- [ ] Verify progress bars update automatically

#### Test 17: Real-time Perk Unlocking

- [ ] Monitor Settings > Referrals while processing referrals
- [ ] Verify achievement popup appears when threshold is reached
- [ ] Verify perk status updates in real-time

### ✅ Error Handling

#### Test 18: Invalid Referral Codes

- [ ] Try signup with non-existent referral code
- [ ] Verify appropriate error handling
- [ ] Ensure signup still completes without referral

#### Test 19: Self-Referral Prevention

- [ ] Try using own referral code during signup
- [ ] Verify system prevents self-referrals
- [ ] Check error handling and user feedback

#### Test 20: Network Error Handling

- [ ] Test with poor network conditions
- [ ] Verify graceful degradation
- [ ] Check error messages are user-friendly

### ✅ Data Integrity

#### Test 21: Referral Data Consistency

- [ ] Verify referral counts match actual referral records
- [ ] Check that perk unlocking is consistent with referral counts
- [ ] Validate Firestore data structure matches specifications

#### Test 22: Migration Validation

- [ ] Run migration scripts multiple times
- [ ] Verify no duplicate data is created
- [ ] Check that existing users are not affected

## Performance Testing

### Test 23: Load Testing

- [ ] Test with multiple concurrent signups using referral codes
- [ ] Monitor Firestore read/write operations
- [ ] Verify system performance under load

### Test 24: Real-time Performance

- [ ] Test real-time updates with multiple users
- [ ] Monitor WebSocket connections
- [ ] Verify no memory leaks in long-running sessions

## Security Testing

### Test 25: Referral Code Security

- [ ] Verify referral codes cannot be guessed easily
- [ ] Test for potential enumeration attacks
- [ ] Check that referral processing is properly authenticated

### Test 26: Data Access Control

- [ ] Verify users can only see their own referral data
- [ ] Test that perk application requires proper authentication
- [ ] Check for potential privilege escalation

## Browser Compatibility

### Test 27: Cross-Browser Testing

- [ ] Test on Chrome, Firefox, Safari, Edge
- [ ] Verify clipboard functionality works across browsers
- [ ] Test mobile browsers (iOS Safari, Chrome Mobile)

### Test 28: Mobile Responsiveness

- [ ] Test referral settings on mobile devices
- [ ] Verify touch targets are appropriately sized
- [ ] Test native share functionality on mobile

## Regression Testing

### Test 29: Existing Feature Compatibility

- [ ] Verify existing signup flow still works without referral codes
- [ ] Test that subscription limits work for non-perk users
- [ ] Ensure existing settings tabs are not affected

### Test 30: Integration Testing

- [ ] Test interaction with existing subscription system
- [ ] Verify AI usage limits work with perk enhancements
- [ ] Test squad and trip creation with enhanced limits

## Post-Testing Validation

After completing all tests:

1. **Data Validation**: Run validation scripts to ensure data integrity
2. **Performance Monitoring**: Set up monitoring for referral system metrics
3. **User Feedback**: Collect initial user feedback on the referral experience
4. **Analytics Setup**: Implement tracking for referral conversion rates

## Known Issues and Limitations

Document any known issues discovered during testing:

- [ ] List any browser-specific issues
- [ ] Note any performance limitations
- [ ] Document any edge cases that need future attention

## Success Criteria

The referral system is considered successfully implemented when:

- ✅ All 30 test cases pass
- ✅ No critical bugs are found
- ✅ Performance meets acceptable standards
- ✅ Security requirements are satisfied
- ✅ User experience is smooth and intuitive

## Rollback Plan

If critical issues are discovered:

1. Disable referral code input in signup form
2. Hide referrals tab in settings
3. Revert to original subscription limit calculations
4. Document issues for future resolution
