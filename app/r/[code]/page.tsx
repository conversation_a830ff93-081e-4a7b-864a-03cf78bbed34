"use client"

import { useEffect, use } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Logo } from "@/components/ui/logo"
import { Loader2 } from "lucide-react"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"

interface ReferralRedirectPageProps {
  params: Promise<{
    code: string
  }>
}

/**
 * Referral short link redirect page
 * Redirects /r/[code] to /signup?referral_code=[code]
 */
export default function ReferralRedirectPage({ params }: ReferralRedirectPageProps) {
  const router = useRouter()
  const { code } = use(params)
  const { user, loading } = useAuthStatus()

  useEffect(() => {
    // Wait for auth status to load
    if (loading) return

    // Validate the referral code format (8 alphanumeric characters)
    const isValidFormat = /^[A-Z0-9]{8}$/.test(code.toUpperCase())

    if (isValidFormat) {
      if (user) {
        // User is already logged in, redirect to dashboard with a message
        router.replace(`/dashboard?referral_code=${code.toUpperCase()}&message=already_logged_in`)
      } else {
        // User is not logged in, redirect to signup with referral code parameter
        router.replace(`/signup?referral_code=${code.toUpperCase()}`)
      }
    } else {
      // Invalid format, redirect appropriately based on auth status
      if (user) {
        router.replace("/dashboard")
      } else {
        router.replace("/signup")
      }
    }
  }, [code, router, user, loading])

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 to-yellow-50 flex flex-col">
      {/* Header */}
      <header className="p-4">
        <Logo />
      </header>

      {/* Main content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              Processing Referral
            </CardTitle>
            <CardDescription>
              {loading
                ? "Checking your account status..."
                : user
                  ? `Redirecting you to dashboard with referral code: ${code.toUpperCase()}`
                  : `Redirecting you to sign up with referral code: ${code.toUpperCase()}`}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-muted-foreground">
              If you're not redirected automatically,
              <a
                href={
                  loading
                    ? "#"
                    : user
                      ? `/dashboard?referral_code=${code.toUpperCase()}&message=already_logged_in`
                      : `/signup?referral_code=${code.toUpperCase()}`
                }
                className="text-primary hover:underline ml-1"
              >
                click here
              </a>
            </p>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
